<!-- <template>
<iframe
  src="https://cloud.fastgpt.cn/chat/share?shareId=q758c44lefhqe9r7ri0atd7w&showHistory=0"
  style="width: 100%; height: 80vh;"
  frameborder="0"
  allow="microphone *; *"
/>

</template> -->

<template>
  <a-card class="chat-box" title="新对话">
    <welcome-card/>
    <t-chat
        ref="chatRef"
        clear-history
        :data="chatList"
        :text-loading="loading"
        :is-stream-load="isStreamLoad"
        style="height: 60vh "
        @clear="clearConfirm"
    >

      <template #content="{ item, index }">
        <t-chat-reasoning
            v-if="!item.content"
        >
          <template #header>
            <t-chat-loading text="思考中..." indicator />
          </template>
        </t-chat-reasoning>
        <t-chat-content :content="item.content" style="padding-bottom: 0px;" />
      </template>
      <template #actions="{ item, index }" >
        <t-chat-action
            :content="item.content"
            :operation-btn="['copy']"
            style=" width: 32px;"
        />
        <div v-if="index == 0 && item.role == 'assistant'" class="quick-topics">
          <div class="topic-item"
               v-for="topic in recommendTopics"
               :key="topic"
               @click="inputEnter(topic)"
          >{{topic}}
            <span class="arrow">→</span>
          </div>
        </div>
      </template>
      <template #footer>
        <!-- <t-chat-input :stop-disabled="isStreamLoad" @send="inputEnter" @stop="onStop"></t-chat-input> -->
        <t-chat-sender
          v-model="inputText"
          :textarea-props="{placeholeder: '请输入消息...'}"
          :loading="isStreamLoad"
          @send="inputEnter"
          @stop="onStop"
          >
          <template #prefix>
            <a-button  shape="round" danger  :size="small" @click="endVoice" v-if="isVoice"> 结束</a-button>
            <a-button shape="round" :size="small" @click="stratVoice" v-if="!isVoice"> 语音 </a-button>
          </template>
          <!-- <template #suffix="{ renderPresets }">
            <component :is="renderPresets([])" />
          </template> -->
        </t-chat-sender>
      </template>
    </t-chat>
  </a-card>
</template>
<script setup>
import {aiApi} from '/@/api/business/mes/ai/ai-api.js'
import { ref,onMounted} from 'vue';
import WelcomeCard from "/@/views/business/mes/ai/chat/components/welcome-card.vue";
import { message } from 'ant-design-vue';
const fetchCancel = ref(null);
const loading = ref(false);
// 流式数据加载中
const isStreamLoad = ref(false);
//对话数据
const chatRef = ref(null);
// 输入框内容
const inputText = ref('');
// 语音状态
const isVoice = ref(false);
// 语音识别
let recognition = null;

//清空消息
async function clearConfirm() {
  await aiApi.chatClear()
  chatList.value = [];
}

// 倒序渲染
const chatList = ref([]);

const onStop = function () {
  if (fetchCancel.value?.controller) {
    fetchCancel.value.controller.abort();
    loading.value = false;
    isStreamLoad.value = false;
  }
};

const stratVoice = function () {
  // 检查浏览器兼容性
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
  if (!SpeechRecognition) {
    message.warning("您的浏览器不支持语音识别功能，请使用最新版本的 Chrome 或 Edge 浏览器");
    return;
  }
  // 如果已经存在实例，先停止之前的
  if (recognition) {
    recognition.stop();
  }
  // 创建新的语音识别实例
  recognition = new SpeechRecognition();
  recognition.lang = 'zh-CN'; // 设置语言为中文
  recognition.interimResults = true; // 启用中间结果
  recognition.continuous = true; // 启用连续识别
  recognition.onstart = () => {
    isVoice.value = true;
    message.success("语音识别已开始，请说话...");
  };
  
  recognition.onresult = (event) => {
    // 获取最新的识别结果
    const transcript = event.results[event.results.length - 1];
    if (transcript.isFinal) {
      inputText.value = transcript[0].transcript;
    }
  };
  recognition.onerror = (event) => {
    console.error('语音识别错误:', event.error);
    isVoice.value = false;
  };  
  // 启动语音识别
  recognition.start();
};

//结束语音识别
const endVoice = function () {
  if (recognition) { 
      recognition.stop();
      message.info("语音识别已结束");
  }
  isVoice.value = false;
};


const inputEnter = function (inputValue) {
  if (isStreamLoad.value || !inputValue) return;

  // 添加用户消息
  chatList.value.unshift({
    // avatar: 'https://tdesign.gtimg.com/site/avatar.jpg',
    name: '自己',
    datetime: new Date().toDateString(),
    content: inputValue,
    role: 'user',

  });

  // 添加助手空消息占位
  chatList.value.unshift({
    // avatar: 'https://tdesign.gtimg.com/site/chat-avatar.png',
    name: '智能助手',
    datetime: new Date().toDateString(),
    content: '',
    role: 'assistant',
  });

  handleData(inputValue);
};

const fetchSSE = async (params, options = {}) => {
  const { success, fail, complete } = options;
  // 创建可取消的请求控制器
  const controller = new AbortController();
  fetchCancel.value = { controller };
  try {
    const responsePromise = aiApi.chatStream(params, controller.signal).catch((message) => {
      const msg = message.toString() || '流式接口异常';
      complete?.(false, msg);
      return Promise.reject(message); //确保后续的错误能被catch获取到
    });

    const response = await responsePromise;
    if (!response.ok) {
      complete?.(false, response.statusText);
      fail?.();
      return;
    }

    const reader = response.body.getReader();//创建一个响应体的读取器
    const decoder = new TextDecoder();//创建一个解码文本
    if (!reader) {
      complete?.(false, '无法读取响应');
      fail?.();
      return;
    }
    let buffer = '';//创建一个缓冲区
    let fullContent = '';//创建一个完整内容区
    async function processText(result) {
      if (result.done) {
        // 流结束时，只有当buffer中有完整的数据行时才尝试处理
        if (buffer) {
          const lines = buffer.split(/\r?\n/).filter(line => line);
          for (const line of lines) {
            if (line && line.startsWith('data:')) {
              try {
                // 尝试解析可能完整的JSON
                const jsonStr = line.slice(5).trim();
                if (jsonStr && jsonStr.endsWith('}')) { // 确保JSON是完整的
                  try {
                    const jsonData = JSON.parse(jsonStr);
                    if (jsonData.ok && jsonData.data?.receive) {
                      const { type, content } = jsonData.data.receive;
                      if (type === 'assistant') {
                        fullContent += content;
                        success?.({ content: fullContent, delta: content });
                      }
                    }
                  } catch (e) {
                    console.error('解析流式数据错误:', e, '原始数据:', line);
                  }
                }
              } catch (e) {
                console.error('解析流式数据错误:', e, '原始数据:', line);
              }
            }
          }
        }
        complete?.(true);
        return;
      }
      const chunk = decoder.decode(result.value, {stream: true});//解码文本，使用stream模式
      console.log(chunk)
      buffer += chunk;//将新的文本添加到缓冲区
      console.log(buffer)
      // 按行分割，但保留缓冲区中可能不完整的最后一行
      const lines = buffer.split(/\r?\n/);
      const incompleteLastLine = lines.pop(); // 保留最后一行，它可能是不完整的
      buffer = incompleteLastLine || '';

      for (const line of lines) {
        if (line && line.startsWith('data:')) {
          try {
            const jsonStr = line.slice(5).trim();//过滤掉空白字符
            if (jsonStr) {
              const jsonData = JSON.parse(jsonStr);

              if (jsonData.ok && jsonData.data?.receive) {
                const { type, content } = jsonData.data.receive;

                if (type === 'assistant') {
                  fullContent += content;
                  success?.({ content: fullContent, delta: content });
                }
              }
            }
          } catch (e) {
            console.error('解析流式数据错误:', e, '原始数据:', line);
          }
        }
      }
      try {

        const nextResult = await reader.read();
        return processText(nextResult);
      } catch (error) {
        if (error.name === 'AbortError' || controller.signal.aborted) {
          complete?.(false, '用户取消请求');
          return;
        }
        console.error('读取流数据错误:', error);
        complete?.(false, error.message || '读取数据异常');
        fail?.();
      }
    }
    reader.read().then(processText).catch((error) => {
      if (error.name !== 'AbortError') {
        console.error('流式请求处理错误:', error);
        complete?.(false, error.message || '请求处理异常');
        fail?.();
      } else {
        complete?.(false, '用户取消请求');
      }
    });
  } catch (error) {
    if (error.name !== 'AbortError') {
      console.error('流式请求错误:', error);
      complete?.(false, error.message || '请求异常');
      fail?.();
    } else {
      complete?.(false, '用户取消请求');
    }
  }
};
//------------------------------输入信息--------------------------
const handleData = async (inputValue) => {
  loading.value = false;
  isStreamLoad.value = true;
  // 清空输入框
  inputText.value = '';
  //清空提示词
  recommendTopics.value = [];
  const lastItem = chatList.value[0];
  try {
    await fetchSSE(
        { content: inputValue },
        {
          success: (data) => {
            lastItem.content = data.content;
          },
          fail: () => {
            lastItem.content = '请求失败，请稍后再试';
            loading.value = false;
            isStreamLoad.value = false;
          },
          complete: (success) => {
            if (success) {
              loading.value = false;
              isStreamLoad.value = false;
              //对话完成后自动请求提示词
              getRecommendTopics();
            }
          }
        }
    );
  } catch (error) {
    console.error('请求异常:', error);
    lastItem.content = '请求异常，请稍后再试';
    loading.value = false;
    isStreamLoad.value = false;
  }
};
//----------------------------请求历史记录--------------------------
async function chatHistory(){
  try{
    const response =(await aiApi.chatHistory()).data

    if(response){
      const historyMessages = response.map(msg =>{
        return{
          // avatar: msg.type == 'user'
          // ? 'https://tdesign.gtimg.com/site/avatar.jpg'
          // : 'https://tdesign.gtimg.com/site/chat-avatar.png',
          name: msg.type == 'user' ? '自己' : '智能助手',
          datetime: new Date().toDateString(),
          content:msg.content,
          role:msg.type
        }
      })
      chatList.value = historyMessages.reverse();
    }
  }catch(error){
    console.error('获取聊天历史记录失败:', error);
  }
}
//------------------------------推荐话题--------------------------
const recommendTopics = ref([])
async function getRecommendTopics(){
  const response = (await aiApi.recommendPrompt()).data
  if(response){
    recommendTopics.value = response
  }
}
onMounted(()=>{
  chatHistory();
})

</script>
<style scoped>
::-webkit-scrollbar-thumb {
  background-color: var(--td-scrollbar-color);
}
::-webkit-scrollbar-thumb:horizontal:hover {
  background-color: var(--td-scrollbar-hover-color);
}
::-webkit-scrollbar-track {
  background-color: var(--td-scroll-track-color);
}
.chat-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.quick-topics {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.topic-item {
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 8px 10px 8px 12px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: fit-content;
}

.topic-item:hover {
  background-color: #eaeaea;
}
.arrow {
  margin-left: 5px;
  font-size: 14px;
}
</style>